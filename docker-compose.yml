services:
 nginx:
   image: vulhub/nginx:1.4.2
   volumes:
    - ./nginx.conf:/usr/local/nginx/conf/nginx.conf
    - ./index.php:/usr/local/nginx/html/index.php
   ports:
    - "8080:80"
 php:
   image: vulhub/php:5.6-fpm
   command: 
    - bash
    - -c
    - "mkdir -p /var/www/html/uploadfiles && chown -R www-data:www-data /var/www/html/uploadfiles && php-fpm"
   volumes:
    - ./index.php:/var/www/html/index.php
    - ./www.conf:/usr/local/etc/php-fpm.d/zz-docker.conf
