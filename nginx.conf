worker_processes  1;

events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    sendfile        on;
    keepalive_timeout  65;

    server {
        listen       80;
        server_name  localhost;
        root   html;
        index  index.php;

        charset utf-8;

        location ~ \.php$ {
           root           html;
           include        fastcgi_params;

           fastcgi_pass   php:9000;
           fastcgi_index  index.php;
           fastcgi_param  SCRIPT_FILENAME  /var/www/html$fastcgi_script_name;
           fastcgi_param  DOCUMENT_ROOT /var/www/html;
        }
    }
}